﻿<?xml version="1.0" encoding="utf-8"?>
<asmv1:assembly xsi:schemaLocation="urn:schemas-microsoft-com:asm.v1 assembly.adaptive.xsd" manifestVersion="1.0" xmlns:asmv1="urn:schemas-microsoft-com:asm.v1" xmlns="urn:schemas-microsoft-com:asm.v2" xmlns:asmv2="urn:schemas-microsoft-com:asm.v2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:co.v1="urn:schemas-microsoft-com:clickonce.v1" xmlns:asmv3="urn:schemas-microsoft-com:asm.v3" xmlns:dsig="http://www.w3.org/2000/09/xmldsig#" xmlns:co.v2="urn:schemas-microsoft-com:clickonce.v2">
  <asmv1:assemblyIdentity name="WindowsFormsApp1.exe" version="*******" publicKeyToken="3e6436de4c478810" language="neutral" processorArchitecture="msil" type="win32" />
  <application />
  <entryPoint>
    <assemblyIdentity name="WindowsFormsApp1" version="*******" language="neutral" processorArchitecture="msil" />
    <commandLine file="WindowsFormsApp1.exe" parameters="" />
  </entryPoint>
  <trustInfo>
    <security>
      <applicationRequestMinimum>
        <PermissionSet Unrestricted="true" ID="Custom" SameSite="site" />
        <defaultAssemblyRequest permissionSetReference="Custom" />
      </applicationRequestMinimum>
      <requestedPrivileges xmlns="urn:schemas-microsoft-com:asm.v3">
        <!--
          UAC 清单选项
          如果要更改 Windows 用户帐户控制级别，请用以下节点之一替换
          requestedExecutionLevel 节点。

        <requestedExecutionLevel  level="asInvoker" uiAccess="false" />
        <requestedExecutionLevel  level="requireAdministrator" uiAccess="false" />
        <requestedExecutionLevel  level="highestAvailable" uiAccess="false" />

          如果要利用文件和注册表虚拟化提供
          向后兼容性，请删除 requestedExecutionLevel 节点。
    -->
        <requestedExecutionLevel level="asInvoker" uiAccess="false" />
      </requestedPrivileges>
    </security>
  </trustInfo>
  <dependency>
    <dependentOS>
      <osVersionInfo>
        <os majorVersion="5" minorVersion="1" buildNumber="2600" servicePackMajor="0" />
      </osVersionInfo>
    </dependentOS>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="Microsoft.Windows.CommonLanguageRuntime" version="4.0.30319.0" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="WindowsFormsApp1.exe" size="94168">
      <assemblyIdentity name="WindowsFormsApp1" version="*******" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha1" />
        <dsig:DigestValue>/eG/cEb5iqdMedHmrT//vSFIgFc=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
<publisherIdentity name="CN=LAPTOP-R9SAKG46\Monster阿原" issuerKeyHash="c2d5c2c626acc836776441fab6ec401863eca0b4" /><Signature Id="StrongNameSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha1" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha1" /><DigestValue>o7S5riJXimGQzw4Ksc8mr2P6bEU=</DigestValue></Reference></SignedInfo><SignatureValue>aXzIgdf0NZ8DSTM8jFpkAZeDxtdSTKZplAk2I3a0COL48E3Or8P6jvWtJ6fNLrjYfCnjC4p2EXUC6tMvnPXcXTXliNQPj5HVOo5d2ZLWn8w1lXTVWCl5C56Sh36V0BRtDffzJx5X5Aaj5CYt156J6lNCdPEK48mHjgIVmtUqt3s=</SignatureValue><KeyInfo Id="StrongNameKeyInfo"><KeyValue><RSAKeyValue><Modulus>lLY3/hl6q5j3Jn56m6B+N3sXIk3pXSyEssvCo5pPlPHNV84ckRA19O9DFK0vEAG2ibRxh/tpuLaP8dw+1JFg44A9D1KMF6lgSRqKHxpwYsYDlsG9iwzjajgcvWCeyA6GMHOMRN6VP5+cN0dcjblVmuBX9fgAfvr4ViwBY+NI9L0=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><msrel:RelData xmlns:msrel="http://schemas.microsoft.com/windows/rel/2005/reldata"><r:license xmlns:r="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:as="http://schemas.microsoft.com/windows/pki/2005/Authenticode"><r:grant><as:ManifestInformation Hash="456cfa63af26cfb10a0ecf90618a5722aeb9b4a3" Description="" Url=""><as:assemblyIdentity name="WindowsFormsApp1.exe" version="*******" publicKeyToken="3e6436de4c478810" language="neutral" processorArchitecture="msil" type="win32" /></as:ManifestInformation><as:SignedBy /><as:AuthenticodePublisher><as:X509SubjectName>CN=LAPTOP-R9SAKG46\Monster阿原</as:X509SubjectName></as:AuthenticodePublisher></r:grant><r:issuer><Signature Id="AuthenticodeSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha1" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha1" /><DigestValue>N1COxTk12CdjUI635JRqkrRJ3ZM=</DigestValue></Reference></SignedInfo><SignatureValue>AbFyDQhhfykeUFpS1yXkpmgrQlb4tooWy/wPQVpfeJ2ziA8mOUhw8k0MqMeM6GCYp2BK7ATaCch/z6Lc1pj+l/LsICod9uMglJdXNdj4Xt+1IuH73T7kn8n73VaLs6pyzlfKHZATlSLNgf5fFdM522vRHjQ3BdsskwOj/Uu0E4I=</SignatureValue><KeyInfo><KeyValue><RSAKeyValue><Modulus>lLY3/hl6q5j3Jn56m6B+N3sXIk3pXSyEssvCo5pPlPHNV84ckRA19O9DFK0vEAG2ibRxh/tpuLaP8dw+1JFg44A9D1KMF6lgSRqKHxpwYsYDlsG9iwzjajgcvWCeyA6GMHOMRN6VP5+cN0dcjblVmuBX9fgAfvr4ViwBY+NI9L0=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><X509Data><X509Certificate>MIIB/TCCAWagAwIBAgIQHtJjmmQD9oFBwrKk5CUGFzANBgkqhkiG9w0BAQUFADA9MTswOQYDVQQDHjIATABBAFAAVABPAFAALQBSADkAUwBBAEsARwA0ADYAXABNAG8AbgBzAHQAZQBylj9TnzAeFw0yMzAxMzEwMjA4MzZaFw0yNDAxMzEwODA4MzZaMD0xOzA5BgNVBAMeMgBMAEEAUABUAE8AUAAtAFIAOQBTAEEASwBHADQANgBcAE0AbwBuAHMAdABlAHKWP1OfMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCUtjf+GXqrmPcmfnqboH43exciTeldLISyy8Kjmk+U8c1XzhyREDX070MUrS8QAbaJtHGH+2m4to/x3D7UkWDjgD0PUowXqWBJGoofGnBixgOWwb2LDONqOBy9YJ7IDoYwc4xE3pU/n5w3R1yNuVWa4Ff1+AB++vhWLAFj40j0vQIDAQABMA0GCSqGSIb3DQEBBQUAA4GBADE3KeJsX5uj+snfm+f0raSOSDG7yZo5Kn23bgtvDpfjtmtx+7/ylCp8xT9Gn4vWFgQwoPlWKn36RI4jVIAUP6HVI4opPidXMEQzLVGJvnTY6JRL8ai3d5W++2kH1/PthOZONPEwHZ1gWk9eKftyL0EyAbBBQwPdQizhmum2dSgP</X509Certificate></X509Data></KeyInfo></Signature></r:issuer></r:license></msrel:RelData></KeyInfo></Signature></asmv1:assembly>